/* Linker script for iBoot-style ARM64 bootloader */

ENTRY(_start)

MEMORY
{
    /* QEMU virt machine loads kernel at 0x40080000 */
    RAM : ORIGIN = 0x40080000, LENGTH = 16M
}

SECTIONS
{
    . = 0x40080000;
    
    /* Boot code section */
    .text : {
        *(.text.boot)
        *(.text)
        *(.text.*)
    } > RAM
    
    /* Read-only data */
    .rodata : {
        *(.rodata)
        *(.rodata.*)
    } > RAM
    
    /* Initialized data */
    .data : {
        *(.data)
        *(.data.*)
    } > RAM
    
    /* Uninitialized data */
    .bss : {
        __bss_start = .;
        *(.bss)
        *(.bss.*)
        *(COMMON)
        __bss_end = .;
    } > RAM
    
    /* Stack space */
    . = ALIGN(16);
    __stack_start = .;
    . += 0x10000; /* 64KB stack */
    __stack_end = .;
    
    /* Heap space */
    . = ALIGN(16);
    __heap_start = .;
    . += 0x100000; /* 1MB heap */
    __heap_end = .;
    
    /* End of bootloader */
    __bootloader_end = .;
}
