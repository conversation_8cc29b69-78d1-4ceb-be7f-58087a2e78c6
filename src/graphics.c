/*
 * iBoot-style ARM64 Bootloader
 * Graphics and framebuffer support
 */

#include "iboot.h"

/* Simple 8x8 bitmap font */
static const uint8_t font_8x8[256][8] = {
    // Basic ASCII characters (simplified)
    [' '] = {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
    ['!'] = {0x18, 0x3C, 0x3C, 0x18, 0x18, 0x00, 0x18, 0x00},
    ['"'] = {0x36, 0x36, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
    ['#'] = {0x36, 0x36, 0x7F, 0x36, 0x7F, 0x36, 0x36, 0x00},
    ['$'] = {0x0C, 0x3E, 0x03, 0x1E, 0x30, 0x1F, 0x0C, 0x00},
    ['%'] = {0x00, 0x63, 0x33, 0x18, 0x0C, 0x66, 0x63, 0x00},
    ['&'] = {0x1C, 0x36, 0x1C, 0x6E, 0x3B, 0x33, 0x6E, 0x00},
    ['\''] = {0x06, 0x06, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00},
    ['('] = {0x18, 0x0C, 0x06, 0x06, 0x06, 0x0C, 0x18, 0x00},
    [')'] = {0x06, 0x0C, 0x18, 0x18, 0x18, 0x0C, 0x06, 0x00},
    ['*'] = {0x00, 0x66, 0x3C, 0xFF, 0x3C, 0x66, 0x00, 0x00},
    ['+'] = {0x00, 0x0C, 0x0C, 0x3F, 0x0C, 0x0C, 0x00, 0x00},
    [','] = {0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x06, 0x00},
    ['-'] = {0x00, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x00},
    ['.'] = {0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x0C, 0x00},
    ['/'] = {0x60, 0x30, 0x18, 0x0C, 0x06, 0x03, 0x01, 0x00},
    ['0'] = {0x3E, 0x63, 0x73, 0x7B, 0x6F, 0x67, 0x3E, 0x00},
    ['1'] = {0x0C, 0x0E, 0x0C, 0x0C, 0x0C, 0x0C, 0x3F, 0x00},
    ['2'] = {0x1E, 0x33, 0x30, 0x1C, 0x06, 0x33, 0x3F, 0x00},
    ['3'] = {0x1E, 0x33, 0x30, 0x1C, 0x30, 0x33, 0x1E, 0x00},
    ['4'] = {0x38, 0x3C, 0x36, 0x33, 0x7F, 0x30, 0x78, 0x00},
    ['5'] = {0x3F, 0x03, 0x1F, 0x30, 0x30, 0x33, 0x1E, 0x00},
    ['6'] = {0x1C, 0x06, 0x03, 0x1F, 0x33, 0x33, 0x1E, 0x00},
    ['7'] = {0x3F, 0x33, 0x30, 0x18, 0x0C, 0x0C, 0x0C, 0x00},
    ['8'] = {0x1E, 0x33, 0x33, 0x1E, 0x33, 0x33, 0x1E, 0x00},
    ['9'] = {0x1E, 0x33, 0x33, 0x3E, 0x30, 0x18, 0x0E, 0x00},
    [':'] = {0x00, 0x0C, 0x0C, 0x00, 0x00, 0x0C, 0x0C, 0x00},
    [';'] = {0x00, 0x0C, 0x0C, 0x00, 0x00, 0x0C, 0x06, 0x00},
    ['<'] = {0x18, 0x0C, 0x06, 0x03, 0x06, 0x0C, 0x18, 0x00},
    ['='] = {0x00, 0x00, 0x3F, 0x00, 0x00, 0x3F, 0x00, 0x00},
    ['>'] = {0x06, 0x0C, 0x18, 0x30, 0x18, 0x0C, 0x06, 0x00},
    ['?'] = {0x1E, 0x33, 0x30, 0x18, 0x0C, 0x00, 0x0C, 0x00},
    ['@'] = {0x3E, 0x63, 0x7B, 0x7B, 0x7B, 0x03, 0x1E, 0x00},
    ['A'] = {0x0C, 0x1E, 0x33, 0x33, 0x3F, 0x33, 0x33, 0x00},
    ['B'] = {0x3F, 0x66, 0x66, 0x3E, 0x66, 0x66, 0x3F, 0x00},
    ['C'] = {0x3C, 0x66, 0x03, 0x03, 0x03, 0x66, 0x3C, 0x00},
    ['D'] = {0x1F, 0x36, 0x66, 0x66, 0x66, 0x36, 0x1F, 0x00},
    ['E'] = {0x7F, 0x46, 0x16, 0x1E, 0x16, 0x46, 0x7F, 0x00},
    ['F'] = {0x7F, 0x46, 0x16, 0x1E, 0x16, 0x06, 0x0F, 0x00},
    ['G'] = {0x3C, 0x66, 0x03, 0x03, 0x73, 0x66, 0x7C, 0x00},
    ['H'] = {0x33, 0x33, 0x33, 0x3F, 0x33, 0x33, 0x33, 0x00},
    ['I'] = {0x1E, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x1E, 0x00},
    ['J'] = {0x78, 0x30, 0x30, 0x30, 0x33, 0x33, 0x1E, 0x00},
    ['K'] = {0x67, 0x66, 0x36, 0x1E, 0x36, 0x66, 0x67, 0x00},
    ['L'] = {0x0F, 0x06, 0x06, 0x06, 0x46, 0x66, 0x7F, 0x00},
    ['M'] = {0x63, 0x77, 0x7F, 0x7F, 0x6B, 0x63, 0x63, 0x00},
    ['N'] = {0x63, 0x67, 0x6F, 0x7B, 0x73, 0x63, 0x63, 0x00},
    ['O'] = {0x1C, 0x36, 0x63, 0x63, 0x63, 0x36, 0x1C, 0x00},
    ['P'] = {0x3F, 0x66, 0x66, 0x3E, 0x06, 0x06, 0x0F, 0x00},
    ['Q'] = {0x1E, 0x33, 0x33, 0x33, 0x3B, 0x1E, 0x38, 0x00},
    ['R'] = {0x3F, 0x66, 0x66, 0x3E, 0x36, 0x66, 0x67, 0x00},
    ['S'] = {0x1E, 0x33, 0x07, 0x0E, 0x38, 0x33, 0x1E, 0x00},
    ['T'] = {0x3F, 0x2D, 0x0C, 0x0C, 0x0C, 0x0C, 0x1E, 0x00},
    ['U'] = {0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x3F, 0x00},
    ['V'] = {0x33, 0x33, 0x33, 0x33, 0x33, 0x1E, 0x0C, 0x00},
    ['W'] = {0x63, 0x63, 0x63, 0x6B, 0x7F, 0x77, 0x63, 0x00},
    ['X'] = {0x63, 0x63, 0x36, 0x1C, 0x1C, 0x36, 0x63, 0x00},
    ['Y'] = {0x33, 0x33, 0x33, 0x1E, 0x0C, 0x0C, 0x1E, 0x00},
    ['Z'] = {0x7F, 0x63, 0x31, 0x18, 0x4C, 0x66, 0x7F, 0x00},
    ['a'] = {0x00, 0x00, 0x1E, 0x30, 0x3E, 0x33, 0x6E, 0x00},
    ['b'] = {0x07, 0x06, 0x06, 0x3E, 0x66, 0x66, 0x3B, 0x00},
    ['c'] = {0x00, 0x00, 0x1E, 0x33, 0x03, 0x33, 0x1E, 0x00},
    ['d'] = {0x38, 0x30, 0x30, 0x3e, 0x33, 0x33, 0x6E, 0x00},
    ['e'] = {0x00, 0x00, 0x1E, 0x33, 0x3f, 0x03, 0x1E, 0x00},
    ['f'] = {0x1C, 0x36, 0x06, 0x0f, 0x06, 0x06, 0x0F, 0x00},
    ['g'] = {0x00, 0x00, 0x6E, 0x33, 0x33, 0x3E, 0x30, 0x1F},
    ['h'] = {0x07, 0x06, 0x36, 0x6E, 0x66, 0x66, 0x67, 0x00},
    ['i'] = {0x0C, 0x00, 0x0E, 0x0C, 0x0C, 0x0C, 0x1E, 0x00},
    ['j'] = {0x30, 0x00, 0x30, 0x30, 0x30, 0x33, 0x33, 0x1E},
    ['k'] = {0x07, 0x06, 0x66, 0x36, 0x1E, 0x36, 0x67, 0x00},
    ['l'] = {0x0E, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x1E, 0x00},
    ['m'] = {0x00, 0x00, 0x33, 0x7F, 0x7F, 0x6B, 0x63, 0x00},
    ['n'] = {0x00, 0x00, 0x1F, 0x33, 0x33, 0x33, 0x33, 0x00},
    ['o'] = {0x00, 0x00, 0x1E, 0x33, 0x33, 0x33, 0x1E, 0x00},
    ['p'] = {0x00, 0x00, 0x3B, 0x66, 0x66, 0x3E, 0x06, 0x0F},
    ['q'] = {0x00, 0x00, 0x6E, 0x33, 0x33, 0x3E, 0x30, 0x78},
    ['r'] = {0x00, 0x00, 0x3B, 0x6E, 0x66, 0x06, 0x0F, 0x00},
    ['s'] = {0x00, 0x00, 0x3E, 0x03, 0x1E, 0x30, 0x1F, 0x00},
    ['t'] = {0x08, 0x0C, 0x3E, 0x0C, 0x0C, 0x2C, 0x18, 0x00},
    ['u'] = {0x00, 0x00, 0x33, 0x33, 0x33, 0x33, 0x6E, 0x00},
    ['v'] = {0x00, 0x00, 0x33, 0x33, 0x33, 0x1E, 0x0C, 0x00},
    ['w'] = {0x00, 0x00, 0x63, 0x6B, 0x7F, 0x7F, 0x36, 0x00},
    ['x'] = {0x00, 0x00, 0x63, 0x36, 0x1C, 0x36, 0x63, 0x00},
    ['y'] = {0x00, 0x00, 0x33, 0x33, 0x33, 0x3E, 0x30, 0x1F},
    ['z'] = {0x00, 0x00, 0x3F, 0x19, 0x0C, 0x26, 0x3F, 0x00},
};

/* Global framebuffer pointer */
static uint32_t *g_framebuffer = NULL;

/*
 * Initialize graphics subsystem
 * For QEMU, we'll use a simple linear framebuffer
 */
int graphics_init(graphics_t *gfx)
{
    if (!gfx) {
        return -1;
    }
    
    // For QEMU virt machine, we need to set up a framebuffer
    // This is a simplified implementation - in reality, we'd need
    // to interact with the display controller
    
    // Allocate framebuffer memory (simplified)
    // In a real implementation, this would be mapped from hardware
    static uint32_t framebuffer_memory[FB_WIDTH * FB_HEIGHT];
    g_framebuffer = framebuffer_memory;
    
    gfx->framebuffer = g_framebuffer;
    gfx->width = FB_WIDTH;
    gfx->height = FB_HEIGHT;
    gfx->pitch = FB_PITCH;
    gfx->bpp = FB_BPP;
    
    // Clear the framebuffer
    graphics_clear(gfx, COLOR_BLACK);
    
    return 0;
}

/*
 * Clear the screen with a solid color
 */
void graphics_clear(graphics_t *gfx, uint32_t color)
{
    if (!gfx || !gfx->framebuffer) {
        return;
    }
    
    uint32_t *fb = gfx->framebuffer;
    uint32_t total_pixels = gfx->width * gfx->height;
    
    for (uint32_t i = 0; i < total_pixels; i++) {
        fb[i] = color;
    }
}

/*
 * Set a single pixel
 */
void graphics_pixel(graphics_t *gfx, int x, int y, uint32_t color)
{
    if (!gfx || !gfx->framebuffer) {
        return;
    }
    
    if (x < 0 || x >= (int)gfx->width || y < 0 || y >= (int)gfx->height) {
        return;
    }
    
    gfx->framebuffer[y * gfx->width + x] = color;
}

/*
 * Draw a filled rectangle
 */
void graphics_rect(graphics_t *gfx, int x, int y, int w, int h, uint32_t color)
{
    if (!gfx || !gfx->framebuffer) {
        return;
    }
    
    for (int dy = 0; dy < h; dy++) {
        for (int dx = 0; dx < w; dx++) {
            graphics_pixel(gfx, x + dx, y + dy, color);
        }
    }
}

/*
 * Draw text using the built-in font
 */
void graphics_text(graphics_t *gfx, int x, int y, const char *text, uint32_t color)
{
    if (!gfx || !gfx->framebuffer || !text) {
        return;
    }
    
    int start_x = x;
    
    while (*text) {
        char c = *text++;
        
        if (c == '\n') {
            y += 8;
            x = start_x;
            continue;
        }
        
        if (c == '\r') {
            x = start_x;
            continue;
        }
        
        // Get font data for character
        const uint8_t *font_data = font_8x8[(unsigned char)c];
        
        // Draw character
        for (int row = 0; row < 8; row++) {
            uint8_t font_row = font_data[row];
            for (int col = 0; col < 8; col++) {
                if (font_row & (0x80 >> col)) {
                    graphics_pixel(gfx, x + col, y + row, color);
                }
            }
        }
        
        x += 8; // Move to next character position
    }
}
