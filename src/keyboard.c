/*
 * iBoot-style ARM64 Bootloader
 * Keyboard input handling
 */

#include "iboot.h"

/* UART base for keyboard input (using UART as keyboard for QEMU) */
#define UART_BASE 0x09000000
#define UART_FR   (*(volatile uint32_t*)(UART_BASE + 0x18))
#define UART_DR   (*(volatile uint32_t*)(UART_BASE + 0x00))
#define UART_FR_RXFE (1 << 4)  // Receive FIFO empty

/* Keyboard state */
static bool g_keyboard_initialized = false;

/*
 * Initialize keyboard subsystem
 */
int keyboard_init(void)
{
    // For QEMU, we'll use UART as keyboard input
    // In a real system, this would initialize PS/2 or USB keyboard
    
    g_keyboard_initialized = true;
    uart_puts("Keyboard initialized (using UART)\r\n");
    
    return 0;
}

/*
 * Check if keyboard input is available
 */
bool keyboard_available(void)
{
    if (!g_keyboard_initialized) {
        return false;
    }
    
    // Check if UART receive FIFO has data
    return !(UART_FR & UART_FR_RXFE);
}

/*
 * Get a character from keyboard (blocking)
 */
int keyboard_getchar(void)
{
    if (!g_keyboard_initialized) {
        return -1;
    }
    
    // Wait for character to be available
    while (!keyboard_available()) {
        // Small delay to prevent busy waiting
        for (volatile int i = 0; i < 1000; i++);
    }
    
    // Read character from UART
    char c = UART_DR & 0xFF;
    
    // Handle special key sequences
    if (c == 27) { // ESC sequence
        // Check for arrow keys
        if (keyboard_available()) {
            char next = UART_DR & 0xFF;
            if (next == '[') {
                if (keyboard_available()) {
                    char arrow = UART_DR & 0xFF;
                    switch (arrow) {
                        case 'A': return 'w'; // Up arrow -> W
                        case 'B': return 's'; // Down arrow -> S
                        case 'C': return 'd'; // Right arrow -> D
                        case 'D': return 'a'; // Left arrow -> A
                    }
                }
            }
        }
        return 27; // Return ESC if not arrow sequence
    }
    
    return c;
}
