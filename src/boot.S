/*
 * iBoot-style ARM64 Bootloader
 * Boot assembly code - entry point and low-level initialization
 */

.section .text.boot
.global _start

/*
 * Entry point - called by QEMU
 * x0 = dtb pointer (device tree blob)
 * x1 = 0
 * x2 = 0
 * x3 = 0
 */
_start:
    // Save device tree pointer
    mov x20, x0
    
    // Get current exception level
    mrs x0, CurrentEL
    lsr x0, x0, #2
    
    // We should be in EL1 or EL2
    cmp x0, #1
    beq el1_entry
    cmp x0, #2
    beq el2_entry
    
    // If we're in EL3, drop to EL2
    cmp x0, #3
    beq el3_to_el2
    
    // Unknown EL, halt
    b halt

el3_to_el2:
    // Configure SCR_EL3
    mov x0, #0x431  // RW=1, HCE=1, SMD=1, RES1 bits
    msr scr_el3, x0
    
    // Configure SPSR_EL3 for EL2h
    mov x0, #0x3c9  // EL2h, DAIF masked
    msr spsr_el3, x0
    
    // Set ELR_EL3 to el2_entry
    ldr x0, =el2_entry
    msr elr_el3, x0
    
    // Exception return to EL2
    eret

el2_entry:
    // Configure HCR_EL2
    mov x0, #0x80000000  // RW=1 (AArch64 for EL1)
    msr hcr_el2, x0
    
    // Configure SPSR_EL2 for EL1h
    mov x0, #0x3c5  // EL1h, DAIF masked
    msr spsr_el2, x0
    
    // Set ELR_EL2 to el1_entry
    ldr x0, =el1_entry
    msr elr_el2, x0
    
    // Exception return to EL1
    eret

el1_entry:
    // Disable interrupts
    msr daifset, #0xf
    
    // Set up stack pointer
    ldr x0, =__stack_end
    mov sp, x0

    // Clear BSS section
    ldr x0, =__bss_start
    ldr x1, =__bss_end
    sub x1, x1, x0      // BSS size
    mov x2, #0
clear_bss:
    cbz x1, bss_cleared
    str x2, [x0], #8
    sub x1, x1, #8
    b clear_bss

bss_cleared:
    // Restore device tree pointer to x0
    mov x0, x20
    
    // Jump to C main function
    bl main
    
    // If main returns, halt
    b halt

halt:
    wfi
    b halt

/*
 * Get current exception level
 */
.global get_current_el
get_current_el:
    mrs x0, CurrentEL
    lsr x0, x0, #2
    ret

/*
 * Disable interrupts
 */
.global disable_interrupts
disable_interrupts:
    msr daifset, #0xf
    ret

/*
 * Enable interrupts
 */
.global enable_interrupts
enable_interrupts:
    msr daifclr, #0xf
    ret

/*
 * Memory barrier
 */
.global memory_barrier
memory_barrier:
    dsb sy
    isb
    ret

/*
 * Cache operations
 */
.global flush_dcache
flush_dcache:
    dc civac, x0
    dsb sy
    ret

.global invalidate_icache
invalidate_icache:
    ic ivau, x0
    dsb sy
    isb
    ret

/*
 * External symbols from linker script
 */
.extern __bss_start
.extern __bss_end
.extern __stack_end
.extern main
