/*
 * iBoot-style ARM64 Bootloader
 * Boot picker interface - Apple-style boot selection UI
 */

#include "iboot.h"

/* Boot picker state */
static int g_selected_option = 0;
static int g_local_num_options = 0;
static boot_option_t *g_options = NULL;
static bool g_picker_initialized = false;

/* External references - these will be set by bootpicker_init */

/*
 * Initialize the boot picker interface
 */
void bootpicker_init(graphics_t *gfx)
{
    if (!gfx) {
        return;
    }

    // Get options from main.c
    extern boot_option_t g_boot_options[MAX_BOOT_OPTIONS];
    extern int g_num_options;

    g_options = g_boot_options;
    g_local_num_options = g_num_options;
    g_selected_option = 0;
    g_picker_initialized = true;

    uart_puts("Boot picker initialized\r\n");
}

/*
 * Draw the Apple-style boot picker interface
 */
void bootpicker_draw(graphics_t *gfx)
{
    if (!gfx || !g_picker_initialized) {
        return;
    }
    
    // Clear screen with dark background
    graphics_clear(gfx, COLOR_BLACK);
    
    // Calculate layout dimensions
    int center_x = gfx->width / 2;
    int center_y = gfx->height / 2;
    
    // Draw Apple logo area (simplified Apple logo using text)
    graphics_text(gfx, center_x - 32, 50, "  Apple  ", COLOR_WHITE);
    graphics_text(gfx, center_x - 32, 70, "iBoot-ARM64", COLOR_LIGHT_GRAY);
    
    // Draw title
    const char *title = "Choose an operating system:";
    int title_width = strlen(title) * 8;
    graphics_text(gfx, center_x - title_width/2, center_y - 100, title, COLOR_WHITE);
    
    // Draw boot options
    int option_start_y = center_y - 50;
    int option_height = 40;
    int option_width = 300;
    int option_x = center_x - option_width/2;
    
    for (int i = 0; i < g_local_num_options; i++) {
        int option_y = option_start_y + (i * (option_height + 10));
        
        // Determine colors based on selection
        uint32_t bg_color = (i == g_selected_option) ? COLOR_APPLE_BLUE : COLOR_DARK_GRAY;
        uint32_t text_color = COLOR_WHITE;
        uint32_t border_color = (i == g_selected_option) ? COLOR_WHITE : COLOR_GRAY;
        
        // Draw option background
        graphics_rect(gfx, option_x, option_y, option_width, option_height, bg_color);
        
        // Draw option border
        // Top border
        graphics_rect(gfx, option_x, option_y, option_width, 2, border_color);
        // Bottom border
        graphics_rect(gfx, option_x, option_y + option_height - 2, option_width, 2, border_color);
        // Left border
        graphics_rect(gfx, option_x, option_y, 2, option_height, border_color);
        // Right border
        graphics_rect(gfx, option_x + option_width - 2, option_y, 2, option_height, border_color);
        
        // Draw option text
        int text_x = option_x + 10;
        int text_y = option_y + 8;
        
        // Option name
        graphics_text(gfx, text_x, text_y, g_options[i].name, text_color);
        
        // Option description (smaller, below name)
        graphics_text(gfx, text_x, text_y + 16, g_options[i].description, COLOR_LIGHT_GRAY);
    }
    
    // Draw selection indicator (arrow)
    if (g_local_num_options > 0) {
        int arrow_x = option_x - 20;
        int arrow_y = option_start_y + (g_selected_option * (option_height + 10)) + option_height/2 - 4;
        graphics_text(gfx, arrow_x, arrow_y, ">", COLOR_WHITE);
    }
    
    // Draw instructions at bottom
    const char *instructions[] = {
        "Use UP/DOWN arrows to select",
        "Press ENTER to boot",
        "Press ESC for options"
    };
    
    int instr_y = gfx->height - 80;
    for (int i = 0; i < 3; i++) {
        int instr_width = strlen(instructions[i]) * 8;
        graphics_text(gfx, center_x - instr_width/2, instr_y + (i * 16), 
                     instructions[i], COLOR_GRAY);
    }
    
    // Draw version info in corner
    graphics_text(gfx, 10, gfx->height - 20, "iBoot-ARM64 v1.0", COLOR_DARK_GRAY);
}

/*
 * Handle keyboard input for boot picker
 * Returns: -1 for no action, option index for boot selection
 */
int bootpicker_handle_input(void)
{
    if (!g_picker_initialized || g_local_num_options == 0) {
        return -1;
    }
    
    // Check if keyboard input is available
    if (!keyboard_available()) {
        return -1;
    }
    
    char key = keyboard_getchar();
    
    switch (key) {
        case 'w':  // Up arrow (or W key)
        case 'A':  // Up arrow escape sequence part
            if (g_selected_option > 0) {
                g_selected_option--;
            } else {
                g_selected_option = g_local_num_options - 1; // Wrap around
            }
            break;
            
        case 's':  // Down arrow (or S key)
        case 'B':  // Down arrow escape sequence part
            if (g_selected_option < g_num_options - 1) {
                g_selected_option++;
            } else {
                g_selected_option = 0; // Wrap around
            }
            break;
            
        case '\r':  // Enter key
        case '\n':
            // Return selected option for booting
            return g_selected_option;
            
        case 27:    // ESC key
            // Show options menu (for future implementation)
            uart_puts("Options menu not implemented yet\r\n");
            break;
            
        case '1':
        case '2':
        case '3':
        case '4':
        case '5':
        case '6':
        case '7':
        case '8':
            // Direct number selection
            {
                int option = key - '1';
                if (option >= 0 && option < g_num_options) {
                    g_selected_option = option;
                    return option;
                }
            }
            break;
            
        default:
            // Ignore other keys
            break;
    }
    
    return -1; // No boot action
}
