/*
 * iBoot-style ARM64 Bootloader
 * Utility functions and basic C library implementations
 */

#include "iboot.h"

/* UART base address for QEMU virt machine */
#define UART_BASE 0x09000000

/* UART registers */
#define UART_DR     (*(volatile uint32_t*)(UART_BASE + 0x00))
#define UART_FR     (*(volatile uint32_t*)(UART_BASE + 0x18))
#define UART_IBRD   (*(volatile uint32_t*)(UART_BASE + 0x24))
#define UART_FBRD   (*(volatile uint32_t*)(UART_BASE + 0x28))
#define UART_LCRH   (*(volatile uint32_t*)(UART_BASE + 0x2C))
#define UART_CR     (*(volatile uint32_t*)(UART_BASE + 0x30))

/* UART flags */
#define UART_FR_TXFF (1 << 5)  // Transmit FIFO full
#define UART_FR_RXFE (1 << 4)  // Receive FIFO empty

/*
 * Memory functions
 */
void *memset(void *s, int c, size_t n)
{
    unsigned char *p = s;
    while (n--) {
        *p++ = (unsigned char)c;
    }
    return s;
}

void *memcpy(void *dest, const void *src, size_t n)
{
    unsigned char *d = dest;
    const unsigned char *s = src;
    while (n--) {
        *d++ = *s++;
    }
    return dest;
}

/*
 * String functions
 */
int strcmp(const char *s1, const char *s2)
{
    while (*s1 && (*s1 == *s2)) {
        s1++;
        s2++;
    }
    return *(unsigned char*)s1 - *(unsigned char*)s2;
}

int strlen(const char *s)
{
    int len = 0;
    while (*s++) {
        len++;
    }
    return len;
}

/*
 * UART functions
 */
void uart_putc(char c)
{
    // Wait for transmit FIFO to have space
    while (UART_FR & UART_FR_TXFF);
    
    // Send character
    UART_DR = c;
}

void uart_puts(const char *s)
{
    while (*s) {
        if (*s == '\n') {
            uart_putc('\r');
        }
        uart_putc(*s++);
    }
}

char uart_getc(void)
{
    // Wait for receive FIFO to have data
    while (UART_FR & UART_FR_RXFE);
    
    // Read character
    return UART_DR & 0xFF;
}

/*
 * Simple printf implementation
 */
static void print_hex(uint64_t value, int digits)
{
    const char hex_chars[] = "0123456789ABCDEF";
    char buffer[17];
    int i;
    
    for (i = digits - 1; i >= 0; i--) {
        buffer[i] = hex_chars[value & 0xF];
        value >>= 4;
    }
    buffer[digits] = '\0';
    
    uart_puts(buffer);
}

static void print_dec(int value)
{
    if (value < 0) {
        uart_putc('-');
        value = -value;
    }
    
    if (value == 0) {
        uart_putc('0');
        return;
    }
    
    char buffer[12];
    int i = 0;
    
    while (value > 0) {
        buffer[i++] = '0' + (value % 10);
        value /= 10;
    }
    
    while (i > 0) {
        uart_putc(buffer[--i]);
    }
}

void printf(const char *fmt, ...)
{
    __builtin_va_list args;
    __builtin_va_start(args, fmt);
    
    while (*fmt) {
        if (*fmt == '%') {
            fmt++;
            switch (*fmt) {
                case 'd':
                    print_dec(__builtin_va_arg(args, int));
                    break;
                case 'x':
                    print_hex(__builtin_va_arg(args, unsigned int), 8);
                    break;
                case 'X':
                    print_hex(__builtin_va_arg(args, unsigned int), 8);
                    break;
                case 'p':
                    uart_puts("0x");
                    print_hex(__builtin_va_arg(args, uint64_t), 16);
                    break;
                case 's':
                    uart_puts(__builtin_va_arg(args, char*));
                    break;
                case 'c':
                    uart_putc(__builtin_va_arg(args, int));
                    break;
                case '%':
                    uart_putc('%');
                    break;
                default:
                    uart_putc('%');
                    uart_putc(*fmt);
                    break;
            }
        } else {
            uart_putc(*fmt);
        }
        fmt++;
    }
    
    __builtin_va_end(args);
}
