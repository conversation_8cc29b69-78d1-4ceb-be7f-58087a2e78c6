/*
 * iBoot-style ARM64 Bootloader
 * Main entry point and initialization
 */

#include "iboot.h"

/* Global variables */
static graphics_t g_graphics;
boot_option_t g_boot_options[MAX_BOOT_OPTIONS];  // Remove static for external access
int g_num_options = 0;  // Remove static for external access

/*
 * Initialize default boot options
 */
static void init_boot_options(void)
{
    // Add some example boot options
    bootpicker_add_option("macOS", "Boot macOS", 0x41000000, 0x1000000);
    bootpicker_add_option("Linux", "Boot Linux Kernel", 0x42000000, 0x800000);
    bootpicker_add_option("Recovery", "Recovery Mode", 0x43000000, 0x500000);
    bootpicker_add_option("Diagnostics", "Hardware Diagnostics", 0x44000000, 0x300000);
}

/*
 * Main bootloader function
 */
void main(void)
{
    // Print startup message
    uart_puts("\r\n");
    uart_puts("iBoot-style Bootloader v1.0\r\n");
    uart_puts("ARM64 Generic Bootloader for QEMU\r\n");
    uart_puts("Copyright (c) 2025\r\n");
    uart_puts("\r\n");
    
    // Get current exception level
    uint64_t el = get_current_el();
    printf("Running at Exception Level: EL%d\r\n", (int)el);
    
    // Initialize graphics subsystem
    uart_puts("Initializing graphics...\r\n");
    if (graphics_init(&g_graphics) != 0) {
        uart_puts("ERROR: Failed to initialize graphics\r\n");
        goto fallback_mode;
    }
    
    // Clear screen with Apple-style background
    graphics_clear(&g_graphics, COLOR_BLACK);
    
    // Initialize keyboard input
    uart_puts("Initializing keyboard...\r\n");
    keyboard_init();
    
    // Initialize boot options
    uart_puts("Setting up boot options...\r\n");
    init_boot_options();
    
    // Initialize boot picker interface
    uart_puts("Starting boot picker...\r\n");
    bootpicker_init(&g_graphics);
    
    // Main boot picker loop
    while (1) {
        // Draw the boot picker interface
        bootpicker_draw(&g_graphics);
        
        // Handle user input
        int input = bootpicker_handle_input();
        
        if (input >= 0 && input < g_num_options) {
            // User selected a boot option
            printf("Booting option %d: %s\r\n", input, g_boot_options[input].name);
            
            // Load and boot the selected kernel
            if (loader_load_kernel(g_boot_options[input].kernel_addr) == 0) {
                uart_puts("Jumping to kernel...\r\n");
                loader_jump_to_kernel(g_boot_options[input].kernel_addr);
            } else {
                uart_puts("ERROR: Failed to load kernel\r\n");
                // Continue in boot picker
            }
        }
        
        // Small delay to prevent busy waiting
        for (volatile int i = 0; i < 100000; i++);
    }

fallback_mode:
    // Fallback to text-only mode
    uart_puts("\r\n=== FALLBACK BOOT MODE ===\r\n");
    uart_puts("Graphics initialization failed, using text mode\r\n");
    uart_puts("\r\n");
    
    init_boot_options();
    
    while (1) {
        uart_puts("\r\nAvailable boot options:\r\n");
        for (int i = 0; i < g_num_options; i++) {
            printf("%d. %s - %s\r\n", i + 1, 
                   g_boot_options[i].name, 
                   g_boot_options[i].description);
        }
        
        uart_puts("\r\nSelect option (1-");
        uart_putc('0' + g_num_options);
        uart_puts("): ");
        
        // Wait for input
        char c = keyboard_getchar();
        uart_putc(c);
        uart_puts("\r\n");
        
        int option = c - '1';
        if (option >= 0 && option < g_num_options) {
            printf("Booting %s...\r\n", g_boot_options[option].name);
            
            if (loader_load_kernel(g_boot_options[option].kernel_addr) == 0) {
                loader_jump_to_kernel(g_boot_options[option].kernel_addr);
            } else {
                uart_puts("ERROR: Failed to load kernel\r\n");
            }
        } else {
            uart_puts("Invalid option\r\n");
        }
    }
}

/*
 * Add a boot option to the list
 */
void bootpicker_add_option(const char *name, const char *desc, uint64_t addr, uint64_t size)
{
    if (g_num_options >= MAX_BOOT_OPTIONS) {
        return;
    }
    
    boot_option_t *opt = &g_boot_options[g_num_options];
    
    // Copy name (truncate if too long)
    int i;
    for (i = 0; i < OPTION_NAME_LEN - 1 && name[i]; i++) {
        opt->name[i] = name[i];
    }
    opt->name[i] = '\0';
    
    // Copy description
    for (i = 0; i < 127 && desc[i]; i++) {
        opt->description[i] = desc[i];
    }
    opt->description[i] = '\0';
    
    opt->kernel_addr = addr;
    opt->kernel_size = size;
    opt->available = true;
    
    g_num_options++;
}
