#ifndef IBOOT_H
#define IBOOT_H

#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>

/* Basic type definitions */
typedef uint8_t  u8;
typedef uint16_t u16;
typedef uint32_t u32;
typedef uint64_t u64;

typedef int8_t   s8;
typedef int16_t  s16;
typedef int32_t  s32;
typedef int64_t  s64;

/* Memory layout */
#define BOOTLOADER_BASE     0x40080000
#define STACK_SIZE          0x10000
#define HEAP_SIZE           0x100000

/* QEMU virt machine memory map */
#define UART0_BASE          0x09000000
#define UART0_SIZE          0x1000
#define RTC_BASE            0x09010000
#define GPIO_BASE           0x09030000
#define PCIE_MMIO_BASE      0x10000000
#define PCIE_MMIO_SIZE      0x2eff0000
#define PCIE_ECAM_BASE      0x3f000000
#define PCIE_ECAM_SIZE      0x1000000
#define PLATFORM_BUS_BASE   0x0c000000
#define PLATFORM_BUS_SIZE   0x2000000

/* Framebuffer configuration */
#define FB_WIDTH            1024
#define FB_HEIGHT           768
#define FB_BPP              32
#define FB_PITCH            (FB_WIDTH * (FB_BPP / 8))

/* Colors (ARGB format) */
#define COLOR_BLACK         0xFF000000
#define COLOR_WHITE         0xFFFFFFFF
#define COLOR_GRAY          0xFF808080
#define COLOR_LIGHT_GRAY    0xFFC0C0C0
#define COLOR_DARK_GRAY     0xFF404040
#define COLOR_BLUE          0xFF0066CC
#define COLOR_APPLE_BLUE    0xFF007AFF

/* Boot picker constants */
#define MAX_BOOT_OPTIONS    8
#define OPTION_NAME_LEN     64

/* Boot option structure */
typedef struct {
    char name[OPTION_NAME_LEN];
    char description[128];
    uint64_t kernel_addr;
    uint64_t kernel_size;
    bool available;
} boot_option_t;

/* Graphics context */
typedef struct {
    uint32_t *framebuffer;
    uint32_t width;
    uint32_t height;
    uint32_t pitch;
    uint32_t bpp;
} graphics_t;

/* Function prototypes */

/* Boot and initialization */
void _start(void);
void main(void);

/* Graphics functions */
int graphics_init(graphics_t *gfx);
void graphics_clear(graphics_t *gfx, uint32_t color);
void graphics_pixel(graphics_t *gfx, int x, int y, uint32_t color);
void graphics_rect(graphics_t *gfx, int x, int y, int w, int h, uint32_t color);
void graphics_text(graphics_t *gfx, int x, int y, const char *text, uint32_t color);

/* Boot picker functions */
void bootpicker_init(graphics_t *gfx);
void bootpicker_draw(graphics_t *gfx);
int bootpicker_handle_input(void);
void bootpicker_add_option(const char *name, const char *desc, uint64_t addr, uint64_t size);

/* Keyboard input */
int keyboard_init(void);
int keyboard_getchar(void);
bool keyboard_available(void);

/* Kernel loader */
int loader_load_kernel(uint64_t addr);
void loader_jump_to_kernel(uint64_t addr);

/* Utility functions */
void *memset(void *s, int c, size_t n);
void *memcpy(void *dest, const void *src, size_t n);
int strcmp(const char *s1, const char *s2);
int strlen(const char *s);
void uart_putc(char c);
void uart_puts(const char *s);
void printf(const char *fmt, ...);

/* Assembly functions */
extern void enable_mmu(void);
extern void disable_interrupts(void);
extern void enable_interrupts(void);
extern uint64_t get_current_el(void);

#endif /* IBOOT_H */
